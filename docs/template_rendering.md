# 通知模板渲染功能

## 概述

本系统集成了强大的模板渲染功能，支持使用 Golang 模板语法和 Sprig 函数库来创建灵活的通知模板。

## 功能特性

- 🎯 **Golang 模板语法**：支持完整的 template/html 模板功能
- 🔧 **Sprig 函数库**：集成了 100+ 个常用函数
- 📝 **分离式模板**：支持标题和内容模板分别定义
- 🔄 **动态数据**：支持告警、故障、空间、模板等多维度数据
- 🎨 **丰富格式**：支持逻辑判断、循环、管道等复杂操作

## 数据结构

模板渲染时可以访问以下数据结构：

```go
type TemplateData struct {
    Alert    map[string]interface{} // 告警数据
    Incident map[string]interface{} // 故障数据  
    Template map[string]interface{} // 模板数据
    Space    map[string]interface{} // 空间数据
}
```

### 告警数据 (Alert)
- `title`: 告警标题
- `description`: 告警描述
- `alert_severity`: 严重程度 (Critical/Warning/Info)
- `created_at`: 创建时间
- `labels`: 标签信息 (map[string]string)
- `id`: 告警ID

### 故障数据 (Incident)
- `id`: 故障ID
- `status`: 故障状态

### 空间数据 (Space)
- `id`: 空间ID
- `name`: 空间名称

## 模板语法示例

### 基础语法

```go
// 访问字段
{{.Alert.title}}
{{.Alert.alert_severity}}

// 条件判断
{{if eq .Alert.alert_severity "Critical"}}
⚠️ 这是严重告警！
{{else}}
ℹ️ 普通告警
{{end}}

// 循环遍历
{{range $key, $value := .Alert.labels}}
- {{$key}}: {{$value}}
{{end}}
```

### Sprig 函数示例

```go
// 字符串处理
{{.Alert.alert_severity | upper}}  // 转大写
{{.Alert.title | title}}           // 首字母大写
{{.Alert.description | truncate 50}} // 截断

// 时间处理
{{now | date "2006-01-02 15:04:05"}}  // 当前时间格式化
{{.Alert.created_at | date "15:04"}}   // 时间格式化

// 默认值
{{.Alert.labels.service | default "未知服务"}}

// 数学运算
{{add 1 2}}  // 加法
{{mul 3 4}}  // 乘法
```

### 复杂模板示例

#### 标题模板
```go
【{{.Alert.alert_severity | upper}}】{{.Alert.title}} - {{.Space.name}}
```

#### 内容模板
```go
🚨 告警详情：

📋 **基本信息**
- 标题：{{.Alert.title}}
- 描述：{{.Alert.description}}
- 严重程度：{{.Alert.alert_severity | upper}}
- 时间：{{.Alert.created_at | date "2006-01-02 15:04:05"}}

🏷️ **标签信息**
{{range $key, $value := .Alert.labels}}
- {{$key | title}}: {{$value}}
{{end}}

🔧 **处理建议**
{{if eq .Alert.alert_severity "Critical"}}
⚠️ 这是一个严重告警，请立即处理！
{{else if eq .Alert.alert_severity "Warning"}}
⚡ 请尽快关注此告警
{{else}}
ℹ️ 请在合适的时间处理此告警
{{end}}

📍 **环境信息**
- 空间：{{.Space.name}}
- 故障ID：{{.Incident.id}}

---
此消息由告警系统自动生成 | {{now | date "2006-01-02 15:04:05"}}
```

## 使用流程

1. **获取空间分派策略**：调用 RPC 获取空间信息，包含通知模板ID
2. **获取通知模板**：根据模板ID调用 RPC 获取模板内容
3. **准备数据**：使用 `PrepareNotificationData` 函数准备通知数据
4. **渲染模板**：调用 `RenderNotificationTemplate` 函数渲染模板
5. **发送通知**：在 `SendToMultipleChannels` 中使用渲染后的内容

## API 接口

### PrepareNotificationData
```go
func PrepareNotificationData(
    alert map[string]interface{}, 
    incident map[string]interface{}, 
    template map[string]interface{}, 
    space map[string]interface{}, 
    recipients []string
) *NotificationData
```

### RenderNotificationTemplate
```go
func RenderNotificationTemplate(ctx context.Context, data *NotificationData) error
```

## 错误处理

- 模板解析失败时，系统会记录错误并使用原始内容
- 渲染失败时，会回退到默认格式
- 确保系统的高可用性和容错性

## 扩展功能

- 支持自定义 Sprig 函数
- 支持模板缓存机制
- 支持多语言模板
- 支持模板版本管理

## 注意事项

1. 模板中的变量名区分大小写
2. 使用 `| default` 函数处理可能为空的字段
3. 复杂逻辑建议拆分为多个简单模板
4. 注意模板的性能影响，避免过度复杂的逻辑

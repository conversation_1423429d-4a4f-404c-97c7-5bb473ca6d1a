# 告警处理流程实现指南

## 1. 原始告警数据入库
- 接收包含集成ID(uuid)和告警JSON数据的请求参数
- 将完整的原始JSON数据保存到`raw_alert`表，确保原始消息完整保留
- 返回生成的原始告警ID，用于后续处理和追踪

## 2. 获取集成配置信息
- 使用集成ID调用`oncall_rpc.GetIntegrationById`服务获取集成详情
- 获取的集成详情应包含：集成类型、配置规则、标签增强规则、告警字段映射、路由规则等

## 3. 告警数据标准化处理
- 使用`rpc/pkg/alertprocessor`包处理不同来源的告警数据
- 实现工厂模式创建对应的处理器(`AlertProcessor`接口):
  - `GrafanaAlertProcessor`
  - `ZabbixAlertProcessor`
  - `PrometheusAlertProcessor`
  - `AliyunAlertProcessor`等
- 处理器负责将原始数据转换为标准`AlertData`结构
- 将标准化后的告警数据保存到`alert`表

## 4. 告警规则处理
- 根据集成详情中的规则配置处理标准化告警
- 实现策略模式处理不同类型的规则（尽量也抽离逻辑 使用工厂模式，每种类型使用一个目录来写逻辑）：
  - 告警分级规则
  - 告警分组规则
  - 告警去重规则
  - 告警抑制规则等
- 更新告警状态和属性

## 5. 获取路由空间信息
- 根据集成详情中的路由配置，调用`oncall_rpc.GetSpaceByID`获取路由空间详情
- 获取空间关联的通知策略、人员分派规则等

## 6. 通知处理与故障创建
- 根据路由空间详情处理通知逻辑（尽量也抽离逻辑 使用工厂模式，每种类型使用一个目录来写逻辑）：
  - 应用分派策略确定通知人员
  - 根据通知渠道配置选择通知方式
  - **调用RPC获取通知模板并渲染内容**：
    - 从空间分派策略中获取通知模板ID
    - 调用`oncall_rpc`获取通知模板内容
    - 使用`PrepareNotificationData`函数准备模板数据
    - 使用Golang模板语法和Sprig函数库渲染模板
    - 支持标题和内容模板分别渲染
  - 应用降噪配置避免过多通知
  - 创建`incident`记录并关联告警
  - 发送通知到相关渠道和人员

## 实现要求
所有步骤应在RPC层实现，确保异步处理、错误重试和分布式锁机制，保证高并发下的可靠性。

## 架构设计原则
- 使用工厂模式处理不同类型的告警源和通知渠道
- 使用策略模式处理不同类型的规则
- 确保代码模块化，每种类型使用独立目录组织
- 保证高并发下的可靠性和性能

## 项目完成情况

### ✅ 已完成功能

#### 1. 基础架构搭建
- [x] Go-Zero框架项目结构初始化
- [x] API服务和RPC服务分离架构
- [x] Docker容器化配置
- [x] 数据库模型设计（使用Ent ORM）
  - [x] RawAlert（原始告警）表结构
  - [x] Alert（标准告警）表结构
  - [x] Incident（故障）表结构
- [x] Proto文件定义和RPC服务接口

#### 2. 原始告警数据入库（步骤1）
- [x] API接口：`POST /api/v1/webhook/:integrationsId`
- [x] 支持接收任意JSON格式的告警数据
- [x] 原始数据完整保存到`raw_alert`表
- [x] API层到RPC层的调用链路
- [x] 基础的错误处理和日志记录

#### 3. 告警数据标准化处理（步骤3）
- [x] 告警处理器工厂模式架构
- [x] 基础告警处理器接口定义
- [x] 已实现的告警处理器：
  - [x] GrafanaAlertProcessor
  - [x] ZabbixAlertProcessor
  - [x] PrometheusAlertProcessor
  - [x] AliyunAlertProcessor
  - [x] DefaultAlertProcessor（默认处理器）
- [x] 告警数据标准化结构定义

#### 4. 告警规则处理框架（步骤4）
- [x] 规则处理器策略模式架构
- [x] 规则类型定义：
  - [x] 告警分级规则（severity）
  - [x] 告警分组规则（grouping）
  - [x] 告警去重规则（deduplication）
  - [x] 告警抑制规则（suppression）
  - [x] 告警收敛规则（convergence）
- [x] 规则处理器基础框架

#### 5. 异步处理队列系统
- [x] Redis队列处理器架构
- [x] 告警消息队列管理
- [x] 分布式锁机制
- [x] 重试机制和错误恢复
- [x] 处理状态跟踪
- [x] 队列处理器启动和停止管理

#### 6. 基础RPC服务
- [x] RawAlertService（原始告警服务）
- [x] AlertService（告警服务）
- [x] IncidentService（故障服务）
- [x] 基础CRUD操作接口

#### 7. 通知处理系统
- [x] 通知管理器和通知器接口设计
- [x] 邮件通知器实现
- [x] 钉钉通知器实现
- [x] Webhook通知器实现
- [x] 多渠道通知发送功能
- [x] 通知结果记录和错误处理
- [x] 通知数据结构设计
- [x] 通知处理器集成到告警处理流程
- [x] **模板渲染功能**：
  - [x] 集成Sprig函数库支持100+常用函数
  - [x] 支持Golang模板语法和复杂逻辑
  - [x] 标题和内容模板分别渲染
  - [x] 多维度数据支持（告警、故障、空间、模板）
  - [x] 模板渲染错误处理和回退机制
  - [x] RPC获取通知模板集成

### 🚧 部分完成功能

#### 1. 告警处理器实现
- [x] 基础框架和接口
- [⚠️] 各类型处理器的具体实现逻辑需要完善
- [⚠️] 告警字段映射和标签增强规则需要实现

#### 2. 规则处理器实现
- [x] 基础框架和接口
- [⚠️] 各类型规则的具体处理逻辑需要实现
- [⚠️] 规则配置解析和应用需要完善

#### 3. 队列处理流程
- [x] 队列基础架构
- [⚠️] 完整的告警处理流程集成需要完善
- [⚠️] 子处理器（alert、incident、notification）实现需要完善

### ❌ 待开发功能

#### 1. 集成配置信息获取（步骤2）
- [ ] 调用`oncall_rpc.GetIntegrationById`服务
- [ ] 集成详情解析和缓存
- [ ] 集成类型、配置规则、路由规则处理

#### 2. 路由空间信息获取（步骤5）
- [ ] 调用`oncall_rpc.GetRouteSpace`服务
- [ ] 路由空间详情解析
- [ ] 通知策略和人员分派规则处理

#### 3. 通知处理与故障创建（步骤6）
- [ ] 通知渠道工厂模式实现
- [ ] 分派策略处理
- [x] ~~通知模板生成~~ (已完成模板渲染功能)
- [ ] 降噪配置应用
- [ ] 故障创建和关联逻辑
- [x] ~~多渠道通知发送~~ (已完成)

#### 4. 完整的告警处理流程集成
- [ ] 端到端的告警处理流程
- [ ] 各步骤之间的数据传递
- [ ] 错误处理和回滚机制
- [ ] 性能优化和监控

#### 5. 高级功能
- [ ] 告警聚合和关联分析
- [ ] 智能降噪算法
- [ ] 告警趋势分析
- [ ] 性能监控和指标收集
- [ ] 配置管理界面

## 开发任务排期

### 第一阶段：核心流程完善（预计2周）

#### Week 1: 集成配置和规则处理完善
**优先级：高**

**任务1.1：集成配置信息获取（2-3天）**
- [ ] 实现`oncall_rpc.GetIntegrationById`客户端调用
- [ ] 集成配置信息解析和验证
- [ ] 集成配置缓存机制
- [ ] 错误处理和重试逻辑
- [ ] 单元测试编写

**任务1.2：告警处理器逻辑完善（2-3天）**
- [ ] 完善Grafana告警处理器字段映射
- [ ] 完善Zabbix告警处理器字段映射
- [ ] 完善Prometheus告警处理器字段映射
- [ ] 完善Aliyun告警处理器字段映射
- [ ] 标签增强规则实现
- [ ] 处理器单元测试

**任务1.3：规则处理器实现（2天）**
- [ ] 告警分级规则具体实现
- [ ] 告警分组规则具体实现
- [ ] 告警去重规则具体实现
- [ ] 规则配置解析器
- [ ] 规则处理器集成测试

#### Week 2: 路由和通知系统
**优先级：高**

**任务2.1：路由空间信息获取（2天）**
- [ ] 实现`oncall_rpc.GetRouteSpace`客户端调用
- [ ] 路由空间信息解析
- [ ] 通知策略解析
- [ ] 人员分派规则解析
- [ ] 路由信息缓存

**任务2.2：通知处理框架（3天）**
- [ ] 通知渠道工厂模式实现
- [ ] 邮件通知渠道实现
- [ ] 短信通知渠道实现
- [ ] 企业微信通知渠道实现
- [ ] 钉钉通知渠道实现
- [ ] 通知模板引擎
- [ ] 通知发送状态跟踪

### 第二阶段：故障管理和流程集成（预计1.5周）

#### Week 3-4: 故障创建和流程集成
**优先级：高**

**任务3.1：故障创建逻辑（2天）**
- [ ] 故障分组规则实现
- [ ] 故障创建和更新逻辑
- [ ] 告警与故障关联
- [ ] 故障状态管理
- [ ] 故障升级机制

**任务3.2：完整流程集成（2天）**
- [ ] 端到端告警处理流程集成
- [ ] 队列处理器完善
- [ ] 子处理器实现（alert、incident、notification）
- [ ] 流程错误处理和回滚
- [ ] 性能优化

**任务3.3：降噪和收敛功能（1-2天）**
- [ ] 告警抑制规则实现
- [ ] 告警收敛规则实现
- [ ] 降噪配置应用
- [ ] 静音功能实现

### 第三阶段：测试和优化（预计1周）

#### Week 5: 测试和文档
**优先级：中**

**任务4.1：测试完善（3天）**
- [ ] 单元测试覆盖率提升至80%+
- [ ] 集成测试编写
- [ ] 性能测试和压力测试
- [ ] 错误场景测试
- [ ] 端到端测试

**任务4.2：文档和部署（2天）**
- [ ] API文档完善
- [ ] 部署文档编写
- [ ] 配置说明文档
- [ ] 故障排查手册
- [ ] 性能调优指南

### 第四阶段：高级功能（预计2周）

#### Week 6-7: 高级功能开发
**优先级：低**

**任务5.1：监控和指标（1周）**
- [ ] 告警处理性能指标收集
- [ ] 系统健康监控
- [ ] 告警统计分析
- [ ] 仪表板集成

**任务5.2：智能功能（1周）**
- [ ] 告警关联分析
- [ ] 智能降噪算法
- [ ] 告警趋势预测
- [ ] 异常检测

## 风险评估和应对

### 主要风险点
1. **外部依赖风险**：`oncall_rpc`服务接口变更或不稳定
   - 应对：提前与相关团队沟通，建立接口变更通知机制

2. **性能风险**：高并发场景下的处理能力
   - 应对：及早进行性能测试，优化队列处理和数据库操作

3. **数据一致性风险**：分布式环境下的数据一致性
   - 应对：完善分布式锁机制，实现幂等性处理

### 里程碑检查点
- **第1周末**：集成配置获取和基础规则处理完成
- **第2周末**：通知系统基础框架完成
- **第4周末**：完整告警处理流程可用
- **第5周末**：系统测试完成，可部署上线
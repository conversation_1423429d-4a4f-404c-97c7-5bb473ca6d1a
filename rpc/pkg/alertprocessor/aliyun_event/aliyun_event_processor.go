package aliyunevent

import (
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/types"
)

// AliyunEventAlertProcessor 阿里云事件数据告警处理器
type AliyunEventAlertProcessor struct{}

// Process 处理阿里云告警数据
func (p *AliyunEventAlertProcessor) Process(rawData map[string]interface{}) (*types.AlertData, error) {
	// 提取阿里云特定的告警信息
	var title, description, severity, alertKey string
	labels := make(map[string]string)

	// 尝试从阿里云告警格式中提取信息
	if alertName, ok := rawData["alertName"].(string); ok {
		title = alertName
	}

	if alertState, ok := rawData["alertState"].(string); ok {
		description = fmt.Sprintf("Alert state: %s", alertState)
	}

	// 提取严重程度
	if level, ok := rawData["level"].(string); ok {
		switch level {
		case "CRITICAL", "P1", "P2":
			severity = "critical"
		case "WARN", "P3":
			severity = "warning"
		default:
			severity = "info"
		}
	}

	// 提取标签
	if instanceName, ok := rawData["instanceName"].(string); ok {
		labels["instance"] = instanceName
	}

	if metricName, ok := rawData["metricName"].(string); ok {
		labels["metric"] = metricName
	}

	// 生成告警去重键
	if ruleId, ok := rawData["ruleId"].(string); ok {
		alertKey = fmt.Sprintf("aliyun-%s", ruleId)
	} else {
		alertKey = fmt.Sprintf("aliyun-%s", title)
	}

	return &types.AlertData{
		Title:       title,
		Description: description,
		Severity:    severity,
		AlertKey:    alertKey,
		Labels:      labels,
		RawData:     rawData,
	}, nil
}

// New 创建阿里云告警处理器
func New() types.AlertProcessor {
	return &AliyunEventAlertProcessor{}
}

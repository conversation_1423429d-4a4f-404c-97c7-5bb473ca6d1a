package alertprocessor

import (
	aliyunevent "gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/aliyun_event"
	aliyunmetrics "gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/aliyun_metrics"
	defaultprocessor "gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/default"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/grafana"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/prometheus"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/zabbix"
)

// AlertData 是types.AlertData的别名，保持向后兼容
type AlertData = types.AlertData

// AlertProcessor 是types.AlertProcessor的别名，保持向后兼容
type AlertProcessor = types.AlertProcessor

// NewAlertProcessor 创建告警处理器
func NewAlertProcessor(integrationType string) (AlertProcessor, error) {
	switch integrationType {
	case "grafana":
		return grafana.New(), nil
	case "zabbix":
		return zabbix.New(), nil
	case "aliyun_merics":
		return aliyunmetrics.New(), nil
	case "aliyun_event":
		return aliyunevent.New(), nil
	case "prometheus":
		return prometheus.New(), nil
	default:
		return defaultprocessor.New(), nil
	}
}

// RegisterCustomProcessor 注册自定义告警处理器
// 这是一个扩展点，允许在不修改核心代码的情况下添加新的处理器
var customProcessors = make(map[string]func() AlertProcessor)

// RegisterProcessor 注册自定义告警处理器
func RegisterProcessor(integrationType string, factory func() AlertProcessor) {
	customProcessors[integrationType] = factory
}

// GetProcessor 获取告警处理器，包括自定义处理器
func GetProcessor(integrationType string) (AlertProcessor, error) {
	// 首先检查是否有自定义处理器
	if factory, exists := customProcessors[integrationType]; exists {
		return factory(), nil
	}

	// 否则使用内置处理器
	return NewAlertProcessor(integrationType)
}

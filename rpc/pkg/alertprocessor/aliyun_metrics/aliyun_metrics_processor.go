package aliyunmetrics

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/alertprocessor/types"
)

// AliyunMericsAlertProcessor 阿里云指标数据告警处理器
type AliyunMericsAlertProcessor struct{}

// InstanceInfo 实例信息结构
type InstanceInfo struct {
	InstanceID   string `json:"instanceId"`
	InstanceName string `json:"instanceName"`
	AliUID       int64  `json:"aliUid"`
	Category     string `json:"category"`
	Region       struct {
		RegionID         string `json:"regionId"`
		AvailabilityZone string `json:"availabilityZone"`
	} `json:"region"`
	NetworkType string `json:"networkType"`
	Tags        []struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	} `json:"tags"`
}

// Process 处理阿里云告警数据
func (p *AliyunMericsAlertProcessor) Process(rawData map[string]interface{}) (*types.AlertData, error) {
	// 提取阿里云特定的告警信息
	var title, description, severity, alertKey string
	labels := make(map[string]string)

	// 提取告警名称作为标题
	if alertName, ok := rawData["alertName"].(string); ok {
		title = alertName
		labels["alert_name"] = alertName
	}

	// 提取告警状态并构建描述
	alertState := ""
	if state, ok := rawData["alertState"].(string); ok {
		alertState = state
		labels["alert_state"] = state
	}

	// 提取当前值和单位
	currentValue := ""
	unit := ""
	if curValue, ok := rawData["curValue"].(string); ok {
		currentValue = curValue
		labels["current_value"] = curValue
	}
	if u, ok := rawData["unit"].(string); ok {
		unit = u
		labels["unit"] = unit
	}

	// 提取表达式
	expression := ""
	if expr, ok := rawData["expression"].(string); ok {
		expression = expr
		labels["expression"] = expr
	}

	// 构建详细描述
	description = p.buildDescription(alertState, currentValue, unit, expression)

	// 根据告警状态和触发级别确定严重程度
	severity = p.determineSeverity(alertState, rawData)

	// 提取触发级别信息
	if triggerLevel, ok := rawData["triggerLevel"].(string); ok {
		labels["trigger_level"] = triggerLevel
	}
	if preTriggerLevel, ok := rawData["preTriggerLevel"].(string); ok && preTriggerLevel != "null" {
		labels["pre_trigger_level"] = preTriggerLevel
	}

	// 提取指标相关信息
	if metricName, ok := rawData["metricName"].(string); ok {
		labels["metric_name"] = metricName
	}
	if rawMetricName, ok := rawData["rawMetricName"].(string); ok {
		labels["raw_metric_name"] = rawMetricName
	}
	if namespace, ok := rawData["namespace"].(string); ok {
		labels["namespace"] = namespace
	}
	if metricProject, ok := rawData["metricProject"].(string); ok {
		labels["metric_project"] = metricProject
	}

	// 提取实例信息
	p.extractInstanceInfo(rawData, labels)

	// 提取地域信息
	if regionName, ok := rawData["regionName"].(string); ok {
		labels["region_name"] = regionName
	}
	if regionId, ok := rawData["regionId"].(string); ok {
		labels["region_id"] = regionId
	}

	// 提取时间信息
	if timestamp, ok := rawData["timestamp"].(string); ok {
		labels["timestamp"] = timestamp
		// 转换时间戳为可读格式
		if ts, err := strconv.ParseInt(timestamp, 10, 64); err == nil {
			labels["alert_time"] = time.Unix(ts/1000, 0).Format("2006-01-02 15:04:05")
		}
	}

	// 提取持续时间
	if lastTime, ok := rawData["lastTime"].(string); ok {
		labels["last_time"] = lastTime
	}

	// 提取产品组信息
	if productGroupName, ok := rawData["productGroupName"].(string); ok {
		labels["product_group"] = productGroupName
	}

	// 生成告警去重键
	if ruleId, ok := rawData["ruleId"].(string); ok {
		alertKey = fmt.Sprintf("aliyun-metrics-%s", ruleId)
		labels["rule_id"] = ruleId
	} else {
		alertKey = fmt.Sprintf("aliyun-metrics-%s", title)
	}

	// 提取事务ID用于关联
	if transId, ok := rawData["transId"].(string); ok {
		labels["trans_id"] = transId
	}

	// 提取组ID
	if groupId, ok := rawData["groupId"].(string); ok {
		labels["group_id"] = groupId
	}

	return &types.AlertData{
		Title:       title,
		Description: description,
		Severity:    severity,
		AlertKey:    alertKey,
		Labels:      labels,
		RawData:     rawData,
	}, nil
}

// buildDescription 构建告警描述
func (p *AliyunMericsAlertProcessor) buildDescription(alertState, currentValue, unit, expression string) string {
	var desc string

	switch alertState {
	case "ALERT":
		desc = fmt.Sprintf("告警触发: 当前值 %s%s", currentValue, unit)
		if expression != "" {
			desc += fmt.Sprintf(", 触发条件: %s", expression)
		}
	case "OK":
		desc = fmt.Sprintf("告警恢复: 当前值 %s%s", currentValue, unit)
		if expression != "" {
			desc += fmt.Sprintf(", 恢复条件: %s", expression)
		}
	default:
		desc = fmt.Sprintf("告警状态: %s, 当前值: %s%s", alertState, currentValue, unit)
	}

	return desc
}

// determineSeverity 根据告警状态和触发级别确定严重程度
func (p *AliyunMericsAlertProcessor) determineSeverity(alertState string, rawData map[string]interface{}) string {
	// 如果是恢复状态，返回info级别
	if alertState == "OK" {
		return "info"
	}

	// 检查触发级别
	if triggerLevel, ok := rawData["triggerLevel"].(string); ok {
		switch triggerLevel {
		case "CRITICAL":
			return "critical"
		case "WARN", "WARNING":
			return "warning"
		case "INFO":
			return "info"
		}
	}

	// 检查前一个触发级别（用于恢复告警）
	if preTriggerLevel, ok := rawData["preTriggerLevel"].(string); ok && preTriggerLevel != "null" {
		switch preTriggerLevel {
		case "CRITICAL":
			return "critical"
		case "WARN", "WARNING":
			return "warning"
		case "INFO":
			return "info"
		}
	}

	// 默认根据告警状态判断
	switch alertState {
	case "ALERT":
		return "warning"
	case "OK":
		return "info"
	default:
		return "info"
	}
}

// extractInstanceInfo 提取实例信息
func (p *AliyunMericsAlertProcessor) extractInstanceInfo(rawData map[string]interface{}, labels map[string]string) {
	// 提取实例名称
	if instanceName, ok := rawData["instanceName"].(string); ok {
		labels["instance_name"] = instanceName
	}

	// 提取用户ID
	if userId, ok := rawData["userId"].(string); ok {
		labels["user_id"] = userId
	}

	// 提取维度信息
	if dimensions, ok := rawData["dimensions"].(string); ok {
		labels["dimensions"] = dimensions
	}

	// 提取原始维度信息
	if dimensionsOriginal, ok := rawData["dimensionsOriginal"].(string); ok {
		labels["dimensions_original"] = dimensionsOriginal
	}

	// 解析实例详细信息JSON
	if instanceInfoStr, ok := rawData["instanceInfo"].(string); ok {
		var instanceInfo InstanceInfo
		if err := json.Unmarshal([]byte(instanceInfoStr), &instanceInfo); err == nil {
			labels["instance_id"] = instanceInfo.InstanceID
			labels["instance_category"] = instanceInfo.Category
			labels["network_type"] = instanceInfo.NetworkType
			labels["availability_zone"] = instanceInfo.Region.AvailabilityZone

			// 提取标签信息
			for _, tag := range instanceInfo.Tags {
				switch tag.Key {
				case "eipAddress":
					labels["eip_address"] = tag.Value
				case "intranetIp":
					labels["intranet_ip"] = tag.Value
				case "hostName":
					labels["host_name"] = tag.Value
				case "os":
					labels["operating_system"] = tag.Value
				case "instanceTypeFamily":
					labels["instance_type_family"] = tag.Value
				case "vpcInstanceId":
					labels["vpc_instance_id"] = tag.Value
				case "vswitchInstanceId":
					labels["vswitch_instance_id"] = tag.Value
				}
			}
		}
	}
}

// New 创建阿里云告警处理器
func New() types.AlertProcessor {
	return &AliyunMericsAlertProcessor{}
}

package channel

import (
	"context"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
)

// EmailNotifier 邮件通知器
type EmailNotifier struct {
	smtpServer   string
	smtpPort     int
	smtpUsername string
	smtpPassword string
	fromAddress  string
}

// NewEmailNotifier 创建邮件通知器
func NewEmailNotifier(smtpServer string, smtpPort int, smtpUsername, smtpPassword, fromAddress string) *EmailNotifier {
	return &EmailNotifier{
		smtpServer:   smtpServer,
		smtpPort:     smtpPort,
		smtpUsername: smtpUsername,
		smtpPassword: smtpPassword,
		fromAddress:  fromAddress,
	}
}

// Type 返回通知渠道类型
func (n *EmailNotifier) Type() notifier.ChannelType {
	return notifier.EmailChannel
}

// Send 发送邮件通知
func (n *EmailNotifier) Send(ctx context.Context, data *notifier.NotificationData) *notifier.NotificationResult {
	// 检查接收人
	if len(data.Recipients) == 0 {
		return &notifier.NotificationResult{
			Success: false,
			Message: "没有指定接收人",
			Error:   fmt.Errorf("没有指定接收人"),
		}
	}

	// 检查邮件内容
	if data.Title == "" || data.Content == "" {
		return &notifier.NotificationResult{
			Success: false,
			Message: "邮件标题或内容为空",
			Error:   fmt.Errorf("邮件标题或内容为空"),
		}
	}

	// 根据严重程度设置邮件主题前缀
	subjectPrefix := "[通知]"
	switch data.Severity {
	case "Critical":
		subjectPrefix = "[严重告警]"
	case "Warning":
		subjectPrefix = "[警告]"
	case "Info":
		subjectPrefix = "[信息]"
	}

	// 使用渲染后的标题和内容，如果没有则使用原始内容
	title := data.Title
	content := data.Content
	if data.RenderedTitle != "" {
		title = data.RenderedTitle
	}
	if data.RenderedContent != "" {
		content = data.RenderedContent
	}

	// 构建邮件主题
	subject := fmt.Sprintf("%s %s", subjectPrefix, title)

	// 构建邮件内容
	body := fmt.Sprintf(`
告警标题: %s
告警描述: %s
严重程度: %s
告警ID: %s
故障ID: %s
`, title, content, data.Severity, data.AlertID, data.IncidentID)

	// 添加标签信息
	if len(data.Labels) > 0 {
		body += "\n标签信息:\n"
		for k, v := range data.Labels {
			body += fmt.Sprintf("- %s: %s\n", k, v)
		}
	}

	// TODO: 实际发送邮件的逻辑
	// 这里只是模拟发送，实际项目中应该集成真实的邮件发送功能
	fmt.Printf("发送邮件到 %v\n主题: %s\n内容: %s\n", data.Recipients, subject, body)

	return &notifier.NotificationResult{
		Success: true,
		Message: fmt.Sprintf("成功发送邮件到 %v", data.Recipients),
	}
}

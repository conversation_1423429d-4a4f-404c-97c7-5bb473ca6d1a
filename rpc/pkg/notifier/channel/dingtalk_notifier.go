package channel

import (
	"context"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
)

// DingTalkNotifier 钉钉通知器
type DingTalkNotifier struct {
	webhookURL string
	secret     string
}

// NewDingTalkNotifier 创建钉钉通知器
func NewDingTalkNotifier(webhookURL, secret string) *DingTalkNotifier {
	return &DingTalkNotifier{
		webhookURL: webhookURL,
		secret:     secret,
	}
}

// Type 返回通知渠道类型
func (n *DingTalkNotifier) Type() notifier.ChannelType {
	return notifier.DingTalkChannel
}

// Send 发送钉钉通知
func (n *DingTalkNotifier) Send(ctx context.Context, data *notifier.NotificationData) *notifier.NotificationResult {
	// 检查钉钉Webhook URL
	if n.webhookURL == "" {
		return &notifier.NotificationResult{
			Success: false,
			Message: "钉钉Webhook URL为空",
			Error:   fmt.Errorf("钉钉Webhook URL为空"),
		}
	}

	// 检查通知内容
	if data.Title == "" {
		return &notifier.NotificationResult{
			Success: false,
			Message: "通知标题为空",
			Error:   fmt.Errorf("通知标题为空"),
		}
	}

	// 根据严重程度设置不同的图标
	var severityIcon string
	switch data.Severity {
	case "Critical":
		severityIcon = "🔴"
	case "Warning":
		severityIcon = "🟠"
	case "Info":
		severityIcon = "🔵"
	default:
		severityIcon = "ℹ️"
	}

	// 使用渲染后的标题和内容，如果没有则使用原始内容
	titleText := data.Title
	contentText := data.Content
	if data.RenderedTitle != "" {
		titleText = data.RenderedTitle
	}
	if data.RenderedContent != "" {
		contentText = data.RenderedContent
	}

	// 构建钉钉消息内容
	title := fmt.Sprintf("%s %s", severityIcon, titleText)

	// 构建markdown格式的消息内容
	markdown := fmt.Sprintf(`
### %s

**告警描述**: %s

**严重程度**: %s

**告警ID**: %s

**故障ID**: %s
`, title, contentText, data.Severity, data.AlertID, data.IncidentID)

	// 添加标签信息
	if len(data.Labels) > 0 {
		markdown += "\n**标签信息**:\n"
		for k, v := range data.Labels {
			markdown += fmt.Sprintf("- %s: %s\n", k, v)
		}
	}

	// 构建@提醒
	var atMobiles []string
	if channelData, ok := data.ChannelData["dingtalk"].(map[string]interface{}); ok {
		if mobiles, ok := channelData["at_mobiles"].([]string); ok {
			atMobiles = mobiles
		}
	}

	// TODO: 实际发送钉钉消息的逻辑
	// 这里只是模拟发送，实际项目中应该集成真实的钉钉发送功能
	fmt.Printf("发送钉钉消息到 %s\n标题: %s\n内容: %s\n@提醒: %v\n", n.webhookURL, title, markdown, atMobiles)

	return &notifier.NotificationResult{
		Success: true,
		Message: "成功发送钉钉通知",
	}
}

package channel

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/notifier"
)

// WebhookNotifier Webhook通知器
type WebhookNotifier struct {
	defaultURL string
}

// NewWebhookNotifier 创建Webhook通知器
func NewWebhookNotifier(defaultURL string) *WebhookNotifier {
	return &WebhookNotifier{
		defaultURL: defaultURL,
	}
}

// Type 返回通知渠道类型
func (n *WebhookNotifier) Type() notifier.ChannelType {
	return notifier.WebhookChannel
}

// Send 发送Webhook通知
func (n *WebhookNotifier) Send(ctx context.Context, data *notifier.NotificationData) *notifier.NotificationResult {
	// 确定Webhook URL
	webhookURL := n.defaultURL
	if channelData, ok := data.ChannelData["webhook"].(map[string]interface{}); ok {
		if url, ok := channelData["url"].(string); ok && url != "" {
			webhookURL = url
		}
	}

	// 检查Webhook URL
	if webhookURL == "" {
		return &notifier.NotificationResult{
			Success: false,
			Message: "Webhook URL为空",
			Error:   fmt.Errorf("Webhook URL为空"),
		}
	}

	// 使用渲染后的标题和内容，如果没有则使用原始内容
	title := data.Title
	content := data.Content
	if data.RenderedTitle != "" {
		title = data.RenderedTitle
	}
	if data.RenderedContent != "" {
		content = data.RenderedContent
	}

	// 构建Webhook请求体
	payload := map[string]interface{}{
		"title":       title,
		"content":     content,
		"alert_id":    data.AlertID,
		"incident_id": data.IncidentID,
		"severity":    data.Severity,
		"labels":      data.Labels,
	}

	// 添加自定义字段
	if channelData, ok := data.ChannelData["webhook"].(map[string]interface{}); ok {
		if customFields, ok := channelData["custom_fields"].(map[string]interface{}); ok {
			for k, v := range customFields {
				payload[k] = v
			}
		}
	}

	// 序列化为JSON
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return &notifier.NotificationResult{
			Success: false,
			Message: "JSON序列化失败",
			Error:   err,
		}
	}

	// TODO: 实际发送Webhook请求的逻辑
	// 这里只是模拟发送，实际项目中应该发送HTTP请求
	fmt.Printf("发送Webhook请求到 %s\n内容: %s\n", webhookURL, string(jsonPayload))

	return &notifier.NotificationResult{
		Success: true,
		Message: fmt.Sprintf("成功发送Webhook请求到 %s", webhookURL),
	}
}

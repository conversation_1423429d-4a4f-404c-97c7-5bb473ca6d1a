package notifier

import (
	"bytes"
	"context"
	"fmt"
	"html/template"

	"github.com/Masterminds/sprig/v3"
)

// TemplateRenderer 模板渲染器
type TemplateRenderer struct{}

// NewTemplateRenderer 创建模板渲染器
func NewTemplateRenderer() *TemplateRenderer {
	return &TemplateRenderer{}
}

// TemplateData 模板数据结构
type TemplateData struct {
	Alert    map[string]interface{} `json:"alert"`    // 告警数据
	Incident map[string]interface{} `json:"incident"` // 故障数据
	Template map[string]interface{} `json:"template"` // 模板数据
	Space    map[string]interface{} `json:"space"`    // 空间数据
}

// RenderTemplate 渲染模板
func (r *TemplateRenderer) RenderTemplate(ctx context.Context, templateContent string, data *TemplateData) (string, error) {
	if templateContent == "" {
		return "", fmt.Errorf("模板内容为空")
	}

	// 创建模板并添加sprig函数
	tmpl, err := template.New("notification").Funcs(sprig.FuncMap()).Parse(templateContent)
	if err != nil {
		return "", fmt.Errorf("解析模板失败: %w", err)
	}

	// 渲染模板
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("渲染模板失败: %w", err)
	}

	return buf.String(), nil
}

// RenderTitle 渲染标题模板
func (r *TemplateRenderer) RenderTitle(ctx context.Context, titleTemplate string, data *TemplateData) (string, error) {
	if titleTemplate == "" {
		// 如果没有标题模板，使用默认格式
		if alertTitle, ok := data.Alert["title"].(string); ok && alertTitle != "" {
			return alertTitle, nil
		}
		return "告警通知", nil
	}

	return r.RenderTemplate(ctx, titleTemplate, data)
}

// RenderContent 渲染内容模板
func (r *TemplateRenderer) RenderContent(ctx context.Context, contentTemplate string, data *TemplateData) (string, error) {
	if contentTemplate == "" {
		// 如果没有内容模板，使用默认格式
		if description, ok := data.Alert["description"].(string); ok && description != "" {
			return description, nil
		}
		return "告警内容", nil
	}

	return r.RenderTemplate(ctx, contentTemplate, data)
}

// PrepareTemplateData 准备模板数据
func PrepareTemplateData(alert, incident, template, space map[string]interface{}) *TemplateData {
	return &TemplateData{
		Alert:    alert,
		Incident: incident,
		Template: template,
		Space:    space,
	}
}

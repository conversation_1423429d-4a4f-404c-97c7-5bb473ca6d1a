package notifier

import (
	"context"
	"testing"
)

func TestTemplateRenderer(t *testing.T) {
	renderer := NewTemplateRenderer()
	ctx := context.Background()

	// 准备测试数据
	alertData := map[string]interface{}{
		"title":          "数据库连接失败",
		"description":    "MySQL数据库连接超时",
		"alert_severity": "Critical",
		"created_at":     "2024-01-15T10:30:00Z",
		"labels": map[string]string{
			"service":     "mysql",
			"environment": "production",
			"region":      "us-west-1",
		},
	}

	incidentData := map[string]interface{}{
		"id":     "incident-123",
		"status": "open",
	}

	templateData := map[string]interface{}{
		"id":   "template-456",
		"name": "数据库告警模板",
	}

	spaceData := map[string]interface{}{
		"id":   "space-789",
		"name": "生产环境空间",
	}

	data := PrepareTemplateData(alertData, incidentData, templateData, spaceData)

	// 测试标题模板渲染
	titleTemplate := "【{{.Alert.alert_severity}}】{{.Alert.title}} - {{.Space.name}}"
	renderedTitle, err := renderer.RenderTitle(ctx, titleTemplate, data)
	if err != nil {
		t.Fatalf("渲染标题失败: %v", err)
	}
	t.Logf("渲染后的标题: %s", renderedTitle)

	// 测试内容模板渲染（使用sprig函数）
	contentTemplate := `🚨 告警详情：

📋 **基本信息**
- 标题：{{.Alert.title}}
- 描述：{{.Alert.description}}
- 严重程度：{{.Alert.alert_severity | upper}}
- 时间：{{.Alert.created_at}}

🏷️ **标签信息**
{{range $key, $value := .Alert.labels}}
- {{$key | title}}: {{$value}}
{{end}}

🔧 **处理建议**
{{if eq .Alert.alert_severity "Critical"}}
⚠️ 这是一个严重告警，请立即处理！
{{else if eq .Alert.alert_severity "Warning"}}
⚡ 请尽快关注此告警
{{else}}
ℹ️ 请在合适的时间处理此告警
{{end}}

📍 **环境信息**
- 空间：{{.Space.name}}
- 故障ID：{{.Incident.id}}

---
此消息由告警系统自动生成 | {{now | date "2006-01-02 15:04:05"}}`

	renderedContent, err := renderer.RenderContent(ctx, contentTemplate, data)
	if err != nil {
		t.Fatalf("渲染内容失败: %v", err)
	}
	t.Logf("渲染后的内容:\n%s", renderedContent)

	// 测试复杂的sprig函数
	complexTemplate := `{{$severity := .Alert.alert_severity}}
{{$color := ""}}
{{if eq $severity "Critical"}}{{$color = "🔴"}}{{end}}
{{if eq $severity "Warning"}}{{$color = "🟡"}}{{end}}
{{if eq $severity "Info"}}{{$color = "🔵"}}{{end}}

{{$color}} **{{$severity | upper}}** 告警

服务：{{.Alert.labels.service | default "未知服务" | title}}
环境：{{.Alert.labels.environment | default "未知环境" | title}}
区域：{{.Alert.labels.region | default "未知区域" | upper}}

{{if .Alert.labels.service}}
{{if eq .Alert.labels.service "mysql"}}
🗄️ 数据库服务告警
{{else if eq .Alert.labels.service "redis"}}
🔄 缓存服务告警
{{else}}
🔧 其他服务告警
{{end}}
{{end}}`

	complexRendered, err := renderer.RenderTemplate(ctx, complexTemplate, data)
	if err != nil {
		t.Fatalf("渲染复杂模板失败: %v", err)
	}
	t.Logf("复杂模板渲染结果:\n%s", complexRendered)
}

func TestNotificationDataRendering(t *testing.T) {
	ctx := context.Background()

	// 创建通知数据
	alertData := map[string]interface{}{
		"title":          "API响应时间过长",
		"description":    "用户API平均响应时间超过5秒",
		"alert_severity": "Warning",
		"id":             "alert-789",
	}

	incidentData := map[string]interface{}{
		"id": "incident-456",
	}

	templateData := map[string]interface{}{
		"id":               "template-123",
		"title_template":   "⚠️ {{.Alert.alert_severity | upper}}: {{.Alert.title}}",
		"content_template": "告警描述: {{.Alert.description}}\n严重程度: {{.Alert.alert_severity}}\n告警ID: {{.Alert.id}}",
	}

	spaceData := map[string]interface{}{
		"id":   "space-321",
		"name": "API监控空间",
	}

	recipients := []string{"<EMAIL>", "<EMAIL>"}

	// 使用PrepareNotificationData创建通知数据
	notificationData := PrepareNotificationData(alertData, incidentData, templateData, spaceData, recipients)

	// 渲染模板
	err := RenderNotificationTemplate(ctx, notificationData)
	if err != nil {
		t.Fatalf("渲染通知模板失败: %v", err)
	}

	t.Logf("原始标题: %s", notificationData.Title)
	t.Logf("渲染后标题: %s", notificationData.RenderedTitle)
	t.Logf("原始内容: %s", notificationData.Content)
	t.Logf("渲染后内容: %s", notificationData.RenderedContent)
}

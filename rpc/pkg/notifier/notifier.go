package notifier

import (
	"context"
	"errors"
	"fmt"
)

// ChannelType 通知渠道类型
type ChannelType string

const (
	EmailChannel    ChannelType = "email"    // 邮件通知
	SMSChannel      ChannelType = "sms"      // 短信通知
	WebhookChannel  ChannelType = "webhook"  // Webhook通知
	DingTalkChannel ChannelType = "dingtalk" // 钉钉通知
	WeChatChannel   ChannelType = "wechat"   // 微信通知
)

// NotificationData 通知数据
type NotificationData struct {
	Title           string                 // 通知标题
	Content         string                 // 通知内容
	AlertID         string                 // 告警ID
	IncidentID      string                 // 故障ID
	Severity        string                 // 严重程度
	Recipients      []string               // 接收人
	Labels          map[string]string      // 标签
	AlertData       map[string]interface{} // 告警数据
	IncidentData    map[string]interface{} // 故障数据
	TemplateID      string                 // 模板ID
	TemplateData    map[string]interface{} // 模板数据
	SpaceData       map[string]interface{} // 空间数据
	ChannelData     map[string]interface{} // 渠道特定数据
	RenderedTitle   string                 // 渲染后的标题
	RenderedContent string                 // 渲染后的内容
}

// NotificationResult 通知结果
type NotificationResult struct {
	Success bool   // 是否成功
	Message string // 结果消息
	Error   error  // 错误信息
}

// Notifier 通知器接口
type Notifier interface {
	// Type 返回通知渠道类型
	Type() ChannelType

	// Send 发送通知
	Send(ctx context.Context, data *NotificationData) *NotificationResult
}

// NotificationManager 通知管理器
type NotificationManager struct {
	notifiers map[ChannelType]Notifier
}

// NewNotificationManager 创建通知管理器
func NewNotificationManager() *NotificationManager {
	return &NotificationManager{
		notifiers: make(map[ChannelType]Notifier),
	}
}

// RegisterNotifier 注册通知器
func (m *NotificationManager) RegisterNotifier(notifier Notifier) {
	m.notifiers[notifier.Type()] = notifier
}

// SendNotification 发送通知
func (m *NotificationManager) SendNotification(ctx context.Context, channelType ChannelType, data *NotificationData) *NotificationResult {
	notifier, exists := m.notifiers[channelType]
	if !exists {
		return &NotificationResult{
			Success: false,
			Message: "通知渠道不存在",
			Error:   errors.New("通知渠道不存在"),
		}
	}

	return notifier.Send(ctx, data)
}

// SendToMultipleChannels 发送到多个渠道
func (m *NotificationManager) SendToMultipleChannels(ctx context.Context, channelTypes []ChannelType, data *NotificationData) map[ChannelType]*NotificationResult {
	results := make(map[ChannelType]*NotificationResult)

	for _, channelType := range channelTypes {
		results[channelType] = m.SendNotification(ctx, channelType, data)
	}

	return results
}

// PrepareNotificationData 准备通知数据
func PrepareNotificationData(alert map[string]interface{}, incident map[string]interface{}, template map[string]interface{}, space map[string]interface{}, recipients []string) *NotificationData {
	// 提取告警标题
	title, _ := alert["title"].(string)
	if title == "" {
		title = "告警通知"
	}

	// 提取告警描述
	description, _ := alert["description"].(string)

	// 提取告警ID和故障ID
	alertID, _ := alert["id"].(string)
	incidentID, _ := incident["id"].(string)

	// 提取严重程度
	severity, _ := alert["alert_severity"].(string)

	// 提取标签
	labels, _ := alert["labels"].(map[string]string)

	// 提取模板ID
	templateID, _ := template["id"].(string)

	return &NotificationData{
		Title:        title,
		Content:      description,
		AlertID:      alertID,
		IncidentID:   incidentID,
		Severity:     severity,
		Recipients:   recipients,
		Labels:       labels,
		AlertData:    alert,
		IncidentData: incident,
		TemplateID:   templateID,
		TemplateData: template,
		SpaceData:    space,
	}
}

// RenderNotificationTemplate 渲染通知模板
func RenderNotificationTemplate(ctx context.Context, data *NotificationData) error {
	renderer := NewTemplateRenderer()

	// 准备模板数据
	templateData := PrepareTemplateData(data.AlertData, data.IncidentData, data.TemplateData, data.SpaceData)

	// 渲染标题
	if titleTemplate, ok := data.TemplateData["title_template"].(string); ok && titleTemplate != "" {
		renderedTitle, err := renderer.RenderTitle(ctx, titleTemplate, templateData)
		if err != nil {
			return fmt.Errorf("渲染标题模板失败: %w", err)
		}
		data.RenderedTitle = renderedTitle
	} else {
		data.RenderedTitle = data.Title
	}

	// 渲染内容
	if contentTemplate, ok := data.TemplateData["content_template"].(string); ok && contentTemplate != "" {
		renderedContent, err := renderer.RenderContent(ctx, contentTemplate, templateData)
		if err != nil {
			return fmt.Errorf("渲染内容模板失败: %w", err)
		}
		data.RenderedContent = renderedContent
	} else {
		data.RenderedContent = data.Content
	}

	return nil
}

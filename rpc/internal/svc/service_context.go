package svc

import (
	"github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/zrpc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/config"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/client/integration"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/client/space"
)

type ServiceContext struct {
	Config         config.Config
	Redis          redis.UniversalClient
	DB             *ent.Client
	IntegrationRpc integration.Integration
	SpaceRpc       space.Space
}

func NewServiceContext(c config.Config) *ServiceContext {
	rds := c.RedisConf.MustNewUniversalRedis()
	integrationRpc := integration.NewIntegration(zrpc.MustNewClient(c.IntegrationRpc))
	spaceRpc := space.NewSpace(zrpc.MustNewClient(c.SpaceRpc))
	db := ent.NewClient(
		ent.Log(logx.Error), // logger
		ent.Driver(c.DatabaseConf.NewNoCacheDriver()),
	)
	return &ServiceContext{
		Config:         c,
		Redis:          rds,
		DB:             db,
		IntegrationRpc: integrationRpc,
		SpaceRpc:       spaceRpc,
	}
}

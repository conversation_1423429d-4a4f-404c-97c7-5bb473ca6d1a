package config

import (
	"github.com/zeromicro/go-zero/zrpc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_common/utils/pgsql"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_common/utils/redis"
)

type Config struct {
	zrpc.RpcServerConf
	DatabaseConf   pgsql.DatabaseConf
	RedisConf      redis.RedisConf
	IntegrationRpc zrpc.RpcClientConf
	SpaceRpc       zrpc.RpcClientConf

	// 通知配置
	// EmailConfig struct {
	// 	SMTPServer string
	// 	SMTPPort   int
	// 	Username   string
	// 	Password   string
	// 	FromEmail  string
	// }

	// DingTalkConfig struct {
	// 	WebhookURL string
	// 	Secret     string
	// }

	// WebhookConfig struct {
	// 	DefaultURL string
	// }
}

Name: message.rpc
ListenOn: 0.0.0.0:1003
# Etcd:
#   Hosts:
#   - 127.0.0.1:2379
#   Key: message.rpc

DatabaseConf:
  Type: postgres
  Host: *************
  Port: 5432
  DBName: zero_duty_message
  Username: root
  Password: owsYMrtwxNMTU6je
  MaxOpenConn: 100
  SSLMode: disable # disable or require

Log:
  ServiceName: messageRpcLogger
  Mode: file
  Path: /Users/<USER>/XSJ/gozero/zero_duty_message/rpc/logs
  Encoding: json
  Level: debug
  Compress: false
  KeepDays: 7
  StackCoolDownMillis: 100

RedisConf:
  Host: *************:6379
  Db: 0
  Pass: owsYMrtwxNMTU6je

Prometheus:
  Host: 0.0.0.0
  Port: 1103
  Path: /metrics

# RPC客户端配置
IntegrationRpc:
  Endpoints:
    - 127.0.0.1:1002
  NonBlock: true

SpaceRpc:
  Endpoints:
    - 127.0.0.1:1002
  NonBlock: true

# # 通知配置
# EmailConfig:
#   SMTPServer: smtp.example.com
#   SMTPPort: 587
#   Username: <EMAIL>
#   Password: password
#   FromEmail: <EMAIL>

# DingTalkConfig:
#   WebhookURL: https://oapi.dingtalk.com/robot/send
#   Secret: your-secret-key

# WebhookConfig:
#   DefaultURL: https://webhook.example.com/alert

# Tracing Analysis

#Telemetry:
#  Name: core-rpc
#  Endpoint: localhost:4317
#  Sampler: 1.0
#  Batcher: otlpgrpc # grpc
Name: message.rpc
ListenOn: 0.0.0.0:1003
Timeout: 6000
# Etcd:
#   Hosts:
#   - 127.0.0.1:2379
#   Key: message.rpc

DatabaseConf:
  Type: postgres
  Host: pgtimescaledb.zeroduty-qa.svc.cluster.local
  #Host: *************
  Port: 5432
  DBName: zero_duty_message
  Username: devops
  Password: owsYMrtwxNMTU6je
  MaxOpenConn: 100
  SSLMode: disable # disable or require

Log:
  ServiceName: messageRpcLogger
  Mode: file
  Path: ./logs
  Encoding: json
  Level: debug
  Compress: false
  KeepDays: 7
  StackCoolDownMillis: 100

RedisConf:
  Host: redis.zeroduty-qa.svc.cluster.local:6379
  # Host: ************:6379
  Db: 0
  Pass: owsYMrtwxNMTU6je

Prometheus:
  Host: 0.0.0.0
  Port: 1103
  Path: /metrics

# RPC客户端配置
SpaceRpc:
  Target: oncall-rpc-svc.zeroduty-qa.svc.cluster.local:1002
  Timeout: 6000
  KeepaliveTime: '6000ms'

IntegrationRpc:
  Target: oncall-rpc-svc.zeroduty-qa.svc.cluster.local:1002
  Timeout: 6000
  KeepaliveTime: '6000ms'

# CasbinConf:
#   ModelText: |
#     [request_definition]
#     r = sub, obj, act
#     [policy_definition]
#     p = sub, obj, act
#     [role_definition]
#     g = _, _
#     [policy_effect]
#     e = some(where (p.eft == allow))
#     [matchers]
#     m = r.sub == p.sub && keyMatch2(r.obj,p.obj) && r.act == p.act

# Tracing Analysis

#Telemetry:
#  Name: core-rpc
#  Endpoint: localhost:4317
#  Sampler: 1.0
#  Batcher: otlpgrpc # grpc
package main

import (
	"context"
	"flag"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/ent/migrate"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/config"
	alertserviceServer "gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/server/alertservice"
	incidentserviceServer "gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/server/incidentservice"
	rawalertserviceServer "gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/server/rawalertservice"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pb/message"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_message/rpc/pkg/runprocessor"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/message.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)
	ctx := svc.NewServiceContext(c)
	// 初始化表结构
	err := ctx.DB.Schema.Create(
		context.Background(),
		migrate.WithDropIndex(true),
		migrate.WithDropColumn(true),
	)
	if err != nil {
		logx.Errorw("DB init failed creating schema resources", logx.Field("err", err))
	}

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		message.RegisterAlertServiceServer(grpcServer, alertserviceServer.NewAlertServiceServer(ctx))
		message.RegisterIncidentServiceServer(grpcServer, incidentserviceServer.NewIncidentServiceServer(ctx))
		message.RegisterRawAlertServiceServer(grpcServer, rawalertserviceServer.NewRawAlertServiceServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	// 启动异步消息处理器
	runprocessor.StartAlertProcessor(ctx)
	defer runprocessor.StopAlertProcessor()

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
